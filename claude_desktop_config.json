{"name": "IBKR Trading Assistant", "description": "Interactive Brokers trading assistant for <PERSON>", "version": "1.0.0", "servers": [{"ibkr-trading": {"command": "/Users/<USER>/projects/b-team/ibkr_server.sh", "args": ["/Users/<USER>/IBKR/b-team/simple_mcp_server.py", "--host", "127.0.0.1", "--client-id", "1"], "env": {"IBKR_AUTO_CONNECT": "false", "IBKR_DEFAULT_EXCHANGE": "SMART", "IBKR_DEFAULT_CURRENCY": "USD", "IBKR_DEFAULT_TIME_IN_FORCE": "DAY"}}}]}