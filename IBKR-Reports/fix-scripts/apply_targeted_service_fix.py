#!/usr/bin/env python3
"""
🔧 TARGETED SERVICE INJECTION FIX
This script implements a surgical fix for the service injection issues
"""

import os
import logging
from pathlib import Path
from datetime import datetime
import shutil
import re

logger = logging.getLogger('service-fix')

class TargetedServiceFix:
    def __init__(self, base_path: str = "/Users/<USER>/projects/b-team/ibkr_mcp_server"):
        self.base_path = Path(base_path)
        self.app_path = self.base_path / "app"
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
    def backup_file(self, file_path: Path):
        """Create backup of a single file"""
        backup_path = file_path.with_suffix(f'.backup_{self.timestamp}')
        shutil.copy2(file_path, backup_path)
        logger.info(f"Backed up: {file_path.name} -> {backup_path.name}")
        return backup_path
    
    def fix_domain_base_class(self):
        """Fix the base domain manager to handle missing services"""
        logger.info("Fixing base domain manager...")
        
        base_domain_path = self.app_path / "ibkr_mcp/infrastructure/base_domain.py"
        if not base_domain_path.exists():
            logger.error(f"Base domain file not found: {base_domain_path}")
            return False
        
        self.backup_file(base_domain_path)
        
        with open(base_domain_path, 'r') as f:
            content = f.read()
        
        # Add emergency service injection to _initialize_implementations
        emergency_injection = '''    def _initialize_implementations(self):
        """Initialize all implementations with dependency injection"""
        try:
            # CRITICAL FIX: Emergency service injection if services are empty
            if not self.services or 'ibkr_service' not in self.services:
                logger.warning(f"{self.config.name}: No services provided, attempting emergency injection")
                try:
                    from services.ibkr_service import ibkr_service as global_ibkr_service
                    if not self.services:
                        self.services = {}
                    self.services['ibkr_service'] = global_ibkr_service
                    logger.info(f"{self.config.name}: Emergency injection successful")
                except Exception as e:
                    logger.error(f"{self.config.name}: Emergency injection failed: {e}")
            
            for name, impl_class in self.config.implementations.items():
                # Inject services into implementation
                service_dependencies = self._resolve_dependencies(impl_class)
                self.implementations[name] = impl_class(**service_dependencies)
            
            self.is_initialized = True
            logger.info(f"{self.config.name} domain initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize {self.config.name} domain: {e}")
            raise'''
        
        # Replace the _initialize_implementations method
        pattern = r'def _initialize_implementations\(self\):.*?(?=\n    def)'
        content = re.sub(pattern, emergency_injection, content, flags=re.DOTALL)
        
        with open(base_domain_path, 'w') as f:
            f.write(content)
        
        logger.info("✅ Fixed base domain manager")
        return True
    
    def fix_specific_domains(self):
        """Fix domain files to properly initialize with services"""
        logger.info("Fixing domain files...")
        
        domains = [
            "historicaldata_domain.py",
            "contracts_domain.py", 
            "account_domain.py",
            "scanner_domain.py",
            "marketdepth_domain.py",
            "tickbytick_domain.py",
            "realtimebars_domain.py",
            "news_domain.py",
            "optionstrading_domain.py",
            "ordermanagement_domain.py",
            "riskmanagement_domain.py",
            "technicalanalysis_domain.py",
            "volatility_surface_domain.py",
            "algorithmictrading_domain.py",
            "backtesting_domain.py"
        ]
        
        for domain_file in domains:
            domain_path = self.app_path / "ibkr_mcp/domains" / domain_file
            if not domain_path.exists():
                logger.warning(f"Domain file not found: {domain_file}")
                continue
            
            self.backup_file(domain_path)
            
            with open(domain_path, 'r') as f:
                content = f.read()
            
            # Fix the __init__ method to handle service injection properly
            # Pattern to match the __init__ method
            init_pattern = r'(def __init__\(self.*?\):\s*\n)(.*?)(super\(\).__init__\(config\))'
            
            def fix_init(match):
                def_line = match.group(1)
                body = match.group(2)
                super_call = match.group(3)
                
                # Extract domain name from the file
                domain_name_match = re.search(r"name='(\w+)'", body)
                if domain_name_match:
                    domain_name = domain_name_match.group(1)
                else:
                    domain_name = "Unknown"
                
                # Extract implementation class
                impl_match = re.search(r"'main_impl':\s*(\w+)", body)
                if impl_match:
                    impl_class = impl_match.group(1)
                else:
                    impl_class = "UnknownImplementation"
                
                # New init body with emergency injection
                new_body = f'''        implementations = {{
            'main_impl': {impl_class}
        }}
        
        # CRITICAL FIX: Get services from registry or use emergency injection
        from ibkr_mcp.infrastructure.domain_registry import registry
        services = registry.services.copy() if hasattr(registry, 'services') and registry.services else {{}}
        
        # Emergency injection if no services
        if not services or 'ibkr_service' not in services:
            try:
                from services.ibkr_service import ibkr_service as global_service
                services['ibkr_service'] = global_service
                logger.warning(f"{domain_name} domain: Using emergency service injection")
            except Exception as e:
                logger.error(f"{domain_name} domain: Failed to inject service: {{e}}")
        
        config = DomainConfig(
            name='{domain_name}',
            implementations=implementations,
            services=services
        )
        
        '''
                return def_line + new_body + super_call
            
            # Apply the fix
            content = re.sub(init_pattern, fix_init, content, flags=re.DOTALL)
            
            # Ensure logging import
            if 'import logging' not in content:
                content = 'import logging\n' + content
                content = re.sub(r'(class \w+Domain.*?:)', r'logger = logging.getLogger(__name__)\n\n\1', content)
            
            with open(domain_path, 'w') as f:
                f.write(content)
            
            logger.info(f"✅ Fixed {domain_file}")
        
        return True
    
    def fix_main_server(self):
        """Fix the main server file to ensure services are registered early"""
        logger.info("Fixing main server file...")
        
        server_path = self.app_path / "backup/ibkr_mcp_server_original.py"
        if not server_path.exists():
            logger.error(f"Server file not found: {server_path}")
            return False
        
        # Copy to main location
        target_path = self.app_path / "ibkr_mcp_server.py"
        shutil.copy2(server_path, target_path)
        
        with open(target_path, 'r') as f:
            content = f.read()
        
        # Find the lifespan function and modify it
        lifespan_pattern = r'(@asynccontextmanager\s*\nasync def lifespan.*?)(# Initialize the service if needed)'
        
        early_registration = '''@asynccontextmanager
async def lifespan(server):
    """
    Manage the server lifespan with EARLY service registration
    """
    global oms_instance, algo_service, market_data_service, rebalancing_service, routing_service, strategy_service
    global historical_data_tools_v2, tickbytick_tools_v2, marketdepth_tools_v2, realtimebars_tools_v2, news_tools_v2, scanner_tools_v2, optionstrading_tools_v2, riskmanagement_tools_v2, technicalanalysis_tools_v2, volatility_surface_tools_v2, algorithmictrading_tools_v2, ordermanagement_tools_v2, account_tools_v2, contracts_tools_v2, backtesting_tools_v2
    
    # Startup
    logger.info("IBKR MCP Server starting up with EARLY service registration")
    
    # CRITICAL FIX: Register services IMMEDIATELY
    from ibkr_mcp.infrastructure.domain_registry import registry
    from services.ibkr_service import ibkr_service
    
    # Register ibkr_service immediately
    early_services = {'ibkr_service': ibkr_service}
    registry.register_services(early_services)
    logger.info("EARLY SERVICE REGISTRATION: ibkr_service registered")

    # Initialize the service if needed'''
        
        content = re.sub(lifespan_pattern, early_registration + r'\n    \2', content, flags=re.DOTALL)
        
        with open(target_path, 'w') as f:
            f.write(content)
        
        logger.info("✅ Fixed main server file")
        return True
    
    def create_test_script(self):
        """Create a test script to verify the fix"""
        test_script = '''#!/usr/bin/env python3
"""
Test script to verify service injection fixes
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add app directory to path
app_dir = Path(__file__).parent / "app"
sys.path.insert(0, str(app_dir))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(message)s'
)

async def test_service_injection():
    """Test that services are properly injected"""
    logger = logging.getLogger('test')
    
    logger.info("Testing service injection...")
    
    # Import services
    from services.ibkr_service import ibkr_service
    logger.info(f"✓ IBKR service imported: {type(ibkr_service)}")
    
    # Import domain tools
    from ibkr_mcp.tools.contracts_tools_v2 import ContractsToolsV2
    from ibkr_mcp.tools.account_tools_v2 import AccountToolsV2
    from ibkr_mcp.tools.historicaldata_tools_v2 import HistoricalDataToolsV2
    
    # Test instantiation
    contracts_tools = ContractsToolsV2()
    account_tools = AccountToolsV2()
    historical_tools = HistoricalDataToolsV2()
    
    logger.info("✓ Tools instantiated")
    
    # Check service injection
    tools_to_check = [
        ("Contracts", contracts_tools),
        ("Account", account_tools),
        ("Historical", historical_tools)
    ]
    
    all_good = True
    for name, tool in tools_to_check:
        if hasattr(tool, 'domain') and hasattr(tool.domain, 'implementations'):
            impl = list(tool.domain.implementations.values())[0]
            has_service = hasattr(impl, 'ibkr_service') and impl.ibkr_service is not None
            logger.info(f"{name}: {'✓' if has_service else '✗'} Service injected")
            if not has_service:
                all_good = False
        else:
            logger.error(f"{name}: ✗ No domain or implementations")
            all_good = False
    
    if all_good:
        logger.info("\\n✅ ALL SERVICES PROPERLY INJECTED!")
    else:
        logger.error("\\n❌ Some services not properly injected")
    
    return all_good

if __name__ == "__main__":
    success = asyncio.run(test_service_injection())
    sys.exit(0 if success else 1)
'''
        
        test_path = self.base_path / "test_service_injection.py"
        with open(test_path, 'w') as f:
            f.write(test_script)
        
        # Make executable
        import stat
        test_path.chmod(test_path.stat().st_mode | stat.S_IEXEC)
        
        logger.info(f"✅ Created test script: {test_path}")
    
    def apply_fixes(self):
        """Apply all targeted fixes"""
        logger.info("=" * 70)
        logger.info("🔧 APPLYING TARGETED SERVICE INJECTION FIXES")
        logger.info("=" * 70)
        
        try:
            # Fix base domain class
            if not self.fix_domain_base_class():
                raise Exception("Failed to fix base domain class")
            
            # Fix individual domains
            if not self.fix_specific_domains():
                raise Exception("Failed to fix domain files")
            
            # Fix main server
            if not self.fix_main_server():
                raise Exception("Failed to fix main server")
            
            # Create test script
            self.create_test_script()
            
            logger.info("=" * 70)
            logger.info("✅ ALL FIXES APPLIED SUCCESSFULLY!")
            logger.info("=" * 70)
            
            logger.info("\n📋 SUMMARY OF FIXES:")
            logger.info("   ✅ Base domain manager: Added emergency service injection")
            logger.info("   ✅ Domain files: Fixed initialization with service retrieval")
            logger.info("   ✅ Main server: Added early service registration")
            logger.info("   ✅ Test script: Created for verification")
            
            logger.info("\n🧪 To test the fixes:")
            logger.info(f"   python {self.base_path}/test_service_injection.py")
            
            logger.info("\n🚀 To start the server:")
            logger.info(f"   cd {self.app_path}")
            logger.info("   python -m ibkr_mcp_server")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to apply fixes: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s | %(levelname)s | %(message)s'
    )
    
    fixer = TargetedServiceFix()
    if fixer.apply_fixes():
        print("\n🎯 SERVICE INJECTION FIXES APPLIED!")
        print("\n✅ The following issues should now be resolved:")
        print("   • Market Data Services - ✅ Connected")
        print("   • Contract Research - ✅ Connected") 
        print("   • Account Management - ✅ Connected")
        print("   • Market Scanner - ✅ Connected")
        exit(0)
    else:
        print("\n❌ Failed to apply fixes!")
        exit(1)
