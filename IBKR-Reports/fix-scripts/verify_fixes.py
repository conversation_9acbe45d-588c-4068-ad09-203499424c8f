#!/usr/bin/env python3
"""
Verification script to check if service injection fixes are properly applied
"""

import sys
import os
from pathlib import Path

# Add app directory to path
app_dir = Path(__file__).parent / "app"
sys.path.insert(0, str(app_dir))

def check_fix_applied():
    """Check if fixes have been applied to key files"""
    print("🔍 Checking if service injection fixes are applied...")
    
    fixes_applied = {
        "base_domain": False,
        "contracts_domain": False,
        "account_domain": False,
        "server_file": False
    }
    
    # Check base domain
    base_path = Path(__file__).parent / "app/ibkr_mcp/infrastructure/base_domain.py"
    if base_path.exists():
        with open(base_path, 'r') as f:
            content = f.read()
            if "Emergency service injection if services are empty" in content:
                fixes_applied["base_domain"] = True
                print("✅ Base domain: Emergency injection code found")
            else:
                print("❌ Base domain: Emergency injection code NOT found")
    
    # Check contracts domain
    contracts_path = Path(__file__).parent / "app/ibkr_mcp/domains/contracts_domain.py"
    if contracts_path.exists():
        with open(contracts_path, 'r') as f:
            content = f.read()
            if "Emergency injection if no services" in content:
                fixes_applied["contracts_domain"] = True
                print("✅ Contracts domain: Emergency injection code found")
            else:
                print("❌ Contracts domain: Emergency injection code NOT found")
    
    # Check account domain
    account_path = Path(__file__).parent / "app/ibkr_mcp/domains/account_domain.py"
    if account_path.exists():
        with open(account_path, 'r') as f:
            content = f.read()
            if "Emergency injection if no services" in content:
                fixes_applied["account_domain"] = True
                print("✅ Account domain: Emergency injection code found")
            else:
                print("❌ Account domain: Emergency injection code NOT found")
    
    # Check server file
    server_path = Path(__file__).parent / "app/ibkr_mcp_server.py"
    if server_path.exists():
        with open(server_path, 'r') as f:
            content = f.read()
            if "EARLY SERVICE REGISTRATION" in content:
                fixes_applied["server_file"] = True
                print("✅ Server file: Early registration code found")
            else:
                print("❌ Server file: Early registration code NOT found")
    
    # Summary
    all_fixed = all(fixes_applied.values())
    
    print("\n📊 SUMMARY:")
    for component, status in fixes_applied.items():
        print(f"   {component}: {'✅ Fixed' if status else '❌ Not Fixed'}")
    
    if all_fixed:
        print("\n✅ ALL FIXES ARE APPLIED!")
        print("\nYou can now:")
        print("1. Start the server:")
        print(f"   cd {Path(__file__).parent}/app")
        print("   python -m ibkr_mcp_server")
        print("\n2. Test the services are working correctly")
    else:
        print("\n❌ Some fixes are missing. Run apply_targeted_service_fix.py")
    
    return all_fixed

if __name__ == "__main__":
    success = check_fix_applied()
    sys.exit(0 if success else 1)
