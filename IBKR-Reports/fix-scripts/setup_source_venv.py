#!/usr/bin/env python3
"""
Setup Virtual Environment in Source Folder
This script creates a virtual environment in the source folder and installs dependencies
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, cwd=None, check=True):
    """Run a command and return the result"""
    print(f"🔧 Running: {' '.join(cmd)}")
    try:
        result = subprocess.run(
            cmd, 
            cwd=cwd, 
            check=check, 
            capture_output=True, 
            text=True
        )
        if result.stdout:
            print(f"📤 Output: {result.stdout.strip()}")
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stdout:
            print(f"📤 Stdout: {e.stdout}")
        if e.stderr:
            print(f"📤 Stderr: {e.stderr}")
        if check:
            raise
        return e

def setup_source_venv():
    """Setup virtual environment in source folder"""
    print("🚀 Setting up Virtual Environment in Source Folder")
    print("=" * 60)
    
    # Define paths
    project_root = Path(__file__).parent
    source_dir = project_root / "ibkr_mcp_server" / "source"
    venv_dir = source_dir / ".venv"
    requirements_file = project_root / "ibkr_mcp_server" / "requirements.txt"
    
    print(f"📁 Project root: {project_root}")
    print(f"📁 Source directory: {source_dir}")
    print(f"📁 Virtual environment: {venv_dir}")
    print(f"📁 Requirements file: {requirements_file}")
    
    # Create source directory if it doesn't exist
    source_dir.mkdir(parents=True, exist_ok=True)
    print(f"✅ Source directory ready: {source_dir}")
    
    # Remove existing venv if it exists
    if venv_dir.exists():
        print(f"🗑️  Removing existing virtual environment...")
        shutil.rmtree(venv_dir)
    
    # Create new virtual environment
    print(f"\n🔨 Creating virtual environment...")
    run_command([sys.executable, "-m", "venv", str(venv_dir)])
    
    # Determine the correct python and pip paths
    if os.name == 'nt':  # Windows
        python_path = venv_dir / "Scripts" / "python.exe"
        pip_path = venv_dir / "Scripts" / "pip.exe"
    else:  # Unix/Linux/macOS
        python_path = venv_dir / "bin" / "python"
        pip_path = venv_dir / "bin" / "pip"
    
    print(f"🐍 Python path: {python_path}")
    print(f"📦 Pip path: {pip_path}")
    
    # Upgrade pip
    print(f"\n📦 Upgrading pip...")
    run_command([str(python_path), "-m", "pip", "install", "--upgrade", "pip"])
    
    # Install core dependencies first
    print(f"\n📦 Installing core dependencies...")
    core_deps = [
        "mcp==1.8.0",
        "fastapi>=0.95.0", 
        "uvicorn>=0.21.1",
        "requests>=2.31.0",
        "python-dotenv>=1.0.0",
        "pydantic>=1.10.13",
        "httpx>=0.18.2"
    ]
    
    for dep in core_deps:
        print(f"📦 Installing {dep}...")
        run_command([str(pip_path), "install", dep])
    
    # Install IBKR dependencies
    print(f"\n📦 Installing IBKR dependencies...")
    ibkr_deps = [
        "ib_async>=1.0.3",
        "ibapi>=9.81.1",
        "matplotlib>=3.7.0",
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        "nest_asyncio>=1.5.6",
        "tenacity>=8.2.2"
    ]
    
    for dep in ibkr_deps:
        print(f"📦 Installing {dep}...")
        try:
            run_command([str(pip_path), "install", dep])
        except subprocess.CalledProcessError:
            print(f"⚠️  Failed to install {dep}, continuing...")
    
    # Install from requirements file if it exists
    if requirements_file.exists():
        print(f"\n📦 Installing from requirements file...")
        try:
            run_command([str(pip_path), "install", "-r", str(requirements_file)], check=False)
        except:
            print("⚠️  Some packages from requirements.txt failed to install, continuing...")
    
    # Verify installation
    print(f"\n🧪 Verifying installation...")
    test_imports = [
        "mcp",
        "fastapi", 
        "uvicorn",
        "matplotlib",
        "pandas",
        "numpy"
    ]
    
    for module in test_imports:
        try:
            result = run_command([str(python_path), "-c", f"import {module}; print(f'✅ {module} imported successfully')"], check=False)
            if result.returncode != 0:
                print(f"❌ Failed to import {module}")
        except:
            print(f"❌ Failed to test import {module}")
    
    # Create activation scripts
    print(f"\n📝 Creating activation scripts...")
    
    # Create activate script for Unix/macOS
    activate_script = source_dir / "activate_venv.sh"
    activate_content = f"""#!/bin/bash
# Activate the source virtual environment
echo "🚀 Activating source virtual environment..."
source "{venv_dir}/bin/activate"
echo "✅ Virtual environment activated!"
echo "🐍 Python: $(which python)"
echo "📦 Pip: $(which pip)"
"""
    
    with open(activate_script, 'w') as f:
        f.write(activate_content)
    
    # Make it executable
    activate_script.chmod(0o755)
    
    # Create a Python script to test the environment
    test_script = source_dir / "test_venv.py"
    test_content = """#!/usr/bin/env python3
import sys
import subprocess

def test_environment():
    print("🧪 Testing Source Virtual Environment")
    print("=" * 40)
    print(f"🐍 Python executable: {sys.executable}")
    print(f"🐍 Python version: {sys.version}")
    
    # Test imports
    modules = ['mcp', 'fastapi', 'matplotlib', 'pandas', 'numpy']
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module} - OK")
        except ImportError:
            print(f"❌ {module} - FAILED")
    
    print("\\n🎉 Environment test complete!")

if __name__ == "__main__":
    test_environment()
"""
    
    with open(test_script, 'w') as f:
        f.write(test_content)
    
    test_script.chmod(0o755)
    
    print(f"\n🎉 Virtual environment setup complete!")
    print(f"\n📋 Summary:")
    print(f"   📁 Location: {venv_dir}")
    print(f"   🐍 Python: {python_path}")
    print(f"   📦 Pip: {pip_path}")
    print(f"   🔧 Activate script: {activate_script}")
    print(f"   🧪 Test script: {test_script}")
    
    print(f"\n🚀 To use the virtual environment:")
    print(f"   1. cd {source_dir}")
    print(f"   2. source activate_venv.sh")
    print(f"   3. python test_venv.py")
    
    return True

def main():
    """Main function"""
    try:
        success = setup_source_venv()
        if success:
            print(f"\n✅ Virtual environment setup completed successfully!")
        else:
            print(f"\n❌ Virtual environment setup failed!")
        return success
    except Exception as e:
        print(f"\n💥 Error during setup: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
