# Supabase MCP Server Setup Instructions

## Prerequisites
1. Node.js 18+ installed
2. npm or yarn package manager
3. Active Supabase project
4. Supabase project credentials

## Installation Steps

### 1. Install Supabase MCP Server
```bash
# Global installation (recommended)
npm install -g @supabase/mcp-server

# Or using npx (no global install needed)
npx @supabase/mcp-server --help
```

### 2. Get Supabase Credentials
Visit your Supabase dashboard:
- Project URL: https://app.supabase.com/project/[your-project]/settings/api
- Anon Key: Found in Project Settings > API
- Service Role Key: Found in Project Settings > API (keep secret!)

### 3. Update Claude Desktop Configuration
Location: ~/Library/Application Support/Claude/claude_desktop_config.json

Replace with the contents from: claude_desktop_config_dual_servers.json

### 4. Configure Environment Variables
Update the environment variables in the Claude config with your actual Supabase credentials.

### 5. Restart Claude Desktop
1. Completely quit Claude Desktop
2. Restart the application
3. Verify both servers appear in the integrations menu

## Expected Result
- IBKR Server: 108 tools
- Supabase Server: ~25-26 tools
- Total: ~133-134 tools

## Verification
After setup, you should see both servers listed in Claude Desktop:
- ibkr-ultimate-108-tools (108)
- supabase (25-26)

## Troubleshooting
- Check Claude Desktop logs if servers don't appear
- Verify Supabase credentials are correct
- Ensure Node.js and npm are properly installed
- Try running `npx @supabase/mcp-server --help` manually to test
