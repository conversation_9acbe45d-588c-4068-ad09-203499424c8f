{"include": ["ibkr_mcp_server/app"], "exclude": ["**/site-packages/**", "**/Library/Frameworks/Python.framework/**", "**/lib/python*/**", "**/typing.py", "**/types.py", "**/collections/**", "**/asyncio/**", "**/cpython/**", "**/stdlib/**"], "extraPaths": ["ibkr_mcp_server/app"], "pythonVersion": "3.13", "typeCheckingMode": "basic", "reportMissingImports": "warning", "reportMissingTypeStubs": false, "reportGeneralTypeIssues": "warning", "reportIndexIssue": "information", "reportArgumentType": "warning", "reportAttributeAccessIssue": "warning", "reportReturnType": "warning", "reportCallIssue": "warning", "reportAssignmentType": "warning", "reportOperatorIssue": "warning", "reportInvalidTypeForm": "information", "reportInvalidTypeArguments": "information", "reportUnsupportedDunderAll": "none", "reportPrivateUsage": "none", "reportConstantRedefinition": "none", "reportIncompatibleMethodOverride": "information", "reportIncompatibleVariableOverride": "information", "reportOverlappingOverload": "information", "reportUninitializedInstanceVariable": "none", "reportCallInDefaultInitializer": "none", "reportUnnecessaryIsInstance": "none", "reportUnnecessaryCast": "none", "reportAssertAlwaysTrue": "none", "reportSelfClsParameterName": "none", "reportImplicitStringConcatenation": "none", "reportUndefinedVariable": "warning", "reportUnboundVariable": "warning", "stubPath": "./stubs"}