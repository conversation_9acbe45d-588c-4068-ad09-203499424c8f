{"mcpServers": {"ibkr-ultimate-108-tools": {"command": "python", "args": ["/path/to/your/ibkr-mcp-server/main.py"], "env": {"IBKR_HOST": "127.0.0.1", "IBKR_PORT": "7497", "IBKR_CLIENT_ID": "1"}}, "supabase": {"command": "npx", "args": ["@supabase/mcp-server"], "env": {"SUPABASE_URL": "your-supabase-project-url", "SUPABASE_ANON_KEY": "your-supabase-anon-key", "SUPABASE_SERVICE_ROLE_KEY": "your-supabase-service-role-key"}}}}