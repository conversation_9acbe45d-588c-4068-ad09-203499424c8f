{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/IBKR/b-team"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAjQpxPzjay5DrCA-uy3QkB27tTouN"}, "disabled": false, "autoApprove": []}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server"], "env": {"SUPABASE_URL": "https://qwpovikethadrfwlyrzh.supabase.co", "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF3cG92aWtldGhhZHJmd2x5cnpoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM5NTU5NzQsImV4cCI6MjA0OTUzMTk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"}, "disabled": false, "autoApprove": []}, "ibkr-trading": {"command": "/Users/<USER>/IBKR/b-team/ibkr_server.sh", "args": ["--host", "127.0.0.1", "--client-id", "1"], "env": {"IBKR_AUTO_CONNECT": "false", "IBKR_DEFAULT_EXCHANGE": "SMART", "IBKR_DEFAULT_CURRENCY": "USD", "IBKR_DEFAULT_TIME_IN_FORCE": "DAY"}, "disabled": false, "autoApprove": ["*"]}}}