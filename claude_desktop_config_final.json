{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/IBKR/b-team"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAjQpxPzjay5DrCA-uy3QkB27tTouN"}, "disabled": false, "autoApprove": []}, "ibkr-trading": {"command": "/Users/<USER>/IBKR/b-team/ibkr_server.sh", "args": ["--host", "127.0.0.1", "--client-id", "1"], "env": {"IBKR_AUTO_CONNECT": "false", "IBKR_DEFAULT_EXCHANGE": "SMART", "IBKR_DEFAULT_CURRENCY": "USD", "IBKR_DEFAULT_TIME_IN_FORCE": "DAY"}, "disabled": false, "autoApprove": ["*"]}}}